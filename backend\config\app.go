package config

import (
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the application
type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	JWT      JWTConfig
	Scraper  ScraperConfig
	Logging  LoggingConfig
	Security SecurityConfig
}



// ServerConfig holds server-related configuration
type ServerConfig struct {
	Port    string
	GinMode string
}

// JWTConfig holds JWT-related configuration
type JWTConfig struct {
	Secret                string
	AccessTokenDuration   time.Duration
	RefreshTokenDuration  time.Duration
}

// ScraperConfig holds scraper-related configuration
type ScraperConfig struct {
	MaxConcurrentJobs int
	ResultsDirectory  string
	CleanupInterval   time.Duration
	MaxResultAge      time.Duration
	ChromeBin         string
	ChromePath        string
	ChromiumFlags     string
}

// LoggingConfig holds logging-related configuration
type LoggingConfig struct {
	Level  string
	Format string
	Output string
}

// SecurityConfig holds security-related configuration
type SecurityConfig struct {
	BcryptCost              int
	MaxLoginAttempts        int
	AccountLockoutDuration  time.Duration
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	// Try to load .env file (ignore error if file doesn't exist)
	_ = godotenv.Load()

	// Load database config
	dbConfig, err := LoadDatabaseConfig()
	if err != nil {
		return nil, err
	}

	config := &Config{
		Server: ServerConfig{
			Port:    getEnv("PORT", "9000"),
			GinMode: getEnv("GIN_MODE", "debug"),
		},
		Database: *dbConfig,
		JWT: JWTConfig{
			Secret:                getEnv("JWT_SECRET", "your-super-secure-jwt-secret-key-change-in-production-2024"),
			AccessTokenDuration:   getEnvAsDuration("JWT_ACCESS_TOKEN_DURATION", "15m"),
			RefreshTokenDuration:  getEnvAsDuration("JWT_REFRESH_TOKEN_DURATION", "7d"),
		},
		Scraper: ScraperConfig{
			MaxConcurrentJobs: getEnvAsInt("MAX_CONCURRENT_JOBS", 5),
			ResultsDirectory:  getEnv("RESULTS_DIRECTORY", "./results"),
			CleanupInterval:   getEnvAsDuration("CLEANUP_INTERVAL", "24h"),
			MaxResultAge:      getEnvAsDuration("MAX_RESULT_AGE", "720h"),
			ChromeBin:         getEnv("CHROME_BIN", "/usr/bin/chromium-browser"),
			ChromePath:        getEnv("CHROME_PATH", "/usr/bin/chromium-browser"),
			ChromiumFlags:     getEnv("CHROMIUM_FLAGS", "--no-sandbox --disable-dev-shm-usage --disable-gpu --headless"),
		},
		Logging: LoggingConfig{
			Level:  getEnv("LOG_LEVEL", "info"),
			Format: getEnv("LOG_FORMAT", "json"),
			Output: getEnv("LOG_OUTPUT", "stdout"),
		},
		Security: SecurityConfig{
			BcryptCost:             getEnvAsInt("BCRYPT_COST", 12),
			MaxLoginAttempts:       getEnvAsInt("MAX_LOGIN_ATTEMPTS", 5),
			AccountLockoutDuration: getEnvAsDuration("ACCOUNT_LOCKOUT_DURATION", "30m"),
		},
	}

	return config, nil
}

// Helper functions
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsDuration(key string, defaultValue string) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	duration, _ := time.ParseDuration(defaultValue)
	return duration
}
