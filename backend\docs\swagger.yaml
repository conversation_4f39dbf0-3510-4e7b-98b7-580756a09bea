basePath: /
definitions:
  auth.LoginRequest:
    properties:
      device_info:
        type: string
      email:
        type: string
      password:
        minLength: 6
        type: string
    required:
    - email
    - password
    type: object
  auth.RefreshRequest:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  auth.RegisterRequest:
    properties:
      email:
        type: string
      first_name:
        type: string
      last_name:
        type: string
      password:
        minLength: 8
        type: string
    required:
    - email
    - first_name
    - last_name
    - password
    type: object
  auth.TokenPair:
    properties:
      access_token:
        type: string
      expires_in:
        type: integer
      refresh_token:
        type: string
      token_type:
        type: string
    type: object
  handler.AuditLogListResponse:
    properties:
      limit:
        type: integer
      logs:
        items:
          $ref: '#/definitions/handler.AuditLogResponse'
        type: array
      page:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  handler.AuditLogResponse:
    properties:
      action:
        type: string
      created_at:
        type: string
      details:
        type: string
      id:
        type: string
      ip_address:
        type: string
      resource_id:
        type: string
      resource_type:
        type: string
      user_agent:
        type: string
      user_email:
        type: string
      user_id:
        type: string
    type: object
  handler.ErrorResponse:
    properties:
      error:
        example: Invalid credentials
        type: string
    type: object
  handler.JobListResponse:
    properties:
      jobs:
        items:
          $ref: '#/definitions/model.JobSummary'
        type: array
      limit:
        example: 10
        type: integer
      page:
        example: 1
        type: integer
      total:
        example: 150
        type: integer
      total_pages:
        example: 15
        type: integer
    type: object
  handler.MessageResponse:
    properties:
      message:
        example: Operation completed successfully
        type: string
    type: object
  handler.QueuePositionResponse:
    properties:
      job_id:
        example: job_123456
        type: string
      position:
        example: 3
        type: integer
    type: object
  handler.SystemLogListResponse:
    properties:
      limit:
        type: integer
      logs:
        items:
          $ref: '#/definitions/handler.SystemLogResponse'
        type: array
      page:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  handler.SystemLogResponse:
    properties:
      component:
        type: string
      created_at:
        type: string
      details:
        type: string
      id:
        type: string
      level:
        type: string
      message:
        type: string
    type: object
  handler.TestScrapeRequest:
    properties:
      comprehensive_mode:
        description: Extract all content automatically
        example: true
        type: boolean
      javascript_enabled:
        example: false
        type: boolean
      selectors:
        additionalProperties:
          type: string
        example:
          '"content"': '"p"}'
          '{"title"': '"h1"'
        type: object
      url:
        example: https://example.com
        type: string
    required:
    - url
    type: object
  handler.UserListResponse:
    properties:
      limit:
        type: integer
      page:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
      users:
        items:
          $ref: '#/definitions/handler.UserResponse'
        type: array
    type: object
  handler.UserResponse:
    properties:
      created_at:
        type: string
      email:
        type: string
      failed_login_attempts:
        type: integer
      first_name:
        type: string
      id:
        type: string
      is_active:
        type: boolean
      last_login:
        type: string
      last_name:
        type: string
      locked_until:
        type: string
      role:
        type: string
    type: object
  model.CreateJobRequest:
    properties:
      config:
        $ref: '#/definitions/model.ScrapingConfig'
      description:
        example: Scrape product data
        type: string
      name:
        example: Product Scraper
        type: string
      priority:
        allOf:
        - $ref: '#/definitions/model.JobPriority'
        example: 2
    required:
    - config
    - name
    type: object
  model.FieldData:
    properties:
      name:
        type: string
      placeholder:
        type: string
      required:
        type: boolean
      type:
        type: string
      value:
        type: string
    type: object
  model.FormData:
    properties:
      action:
        type: string
      fields:
        items:
          $ref: '#/definitions/model.FieldData'
        type: array
      method:
        type: string
    type: object
  model.HeadingData:
    properties:
      level:
        type: integer
      text:
        type: string
    type: object
  model.ImageData:
    properties:
      alt:
        type: string
      height:
        type: string
      title:
        type: string
      url:
        type: string
      width:
        type: string
    type: object
  model.Job:
    properties:
      completed_at:
        example: "2023-07-18T11:00:00Z"
        type: string
      config:
        $ref: '#/definitions/model.ScrapingConfig'
      created_at:
        example: "2023-07-18T10:30:00Z"
        type: string
      description:
        example: Scrape product information from e-commerce site
        type: string
      error_msg:
        example: Connection timeout
        type: string
      id:
        type: string
      job_id:
        example: job_123456
        type: string
      name:
        example: E-commerce Product Scraper
        type: string
      priority:
        allOf:
        - $ref: '#/definitions/model.JobPriority'
        example: 2
      progress:
        example: 75
        type: integer
      result_count:
        example: 150
        type: integer
      result_url:
        example: https://storage.example.com/results/job_123456.json
        type: string
      started_at:
        example: "2023-07-18T10:35:00Z"
        type: string
      status:
        allOf:
        - $ref: '#/definitions/model.JobStatus'
        example: pending
      success_rate:
        example: 95.5
        type: number
      total_load_time_ms:
        example: 45000
        type: integer
      updated_at:
        example: "2023-07-18T10:30:00Z"
        type: string
      user_email:
        description: Computed field
        example: <EMAIL>
        type: string
      user_id:
        type: string
    required:
    - name
    type: object
  model.JobPriority:
    enum:
    - 1
    - 2
    - 3
    - 4
    type: integer
    x-enum-varnames:
    - PriorityLow
    - PriorityNormal
    - PriorityHigh
    - PriorityCritical
  model.JobStats:
    properties:
      completed_jobs:
        example: 1200
        type: integer
      failed_jobs:
        example: 42
        type: integer
      pending_jobs:
        example: 5
        type: integer
      running_jobs:
        example: 3
        type: integer
      total_jobs:
        example: 1250
        type: integer
    type: object
  model.JobStatus:
    enum:
    - pending
    - queued
    - running
    - completed
    - failed
    - cancelled
    type: string
    x-enum-varnames:
    - JobStatusPending
    - JobStatusQueued
    - JobStatusRunning
    - JobStatusCompleted
    - JobStatusFailed
    - JobStatusCancelled
  model.JobSummary:
    properties:
      created_at:
        example: "2023-07-18T10:30:00Z"
        type: string
      id:
        example: job_123456
        type: string
      name:
        example: Product Scraper
        type: string
      priority:
        allOf:
        - $ref: '#/definitions/model.JobPriority'
        example: 2
      progress:
        example: 75
        type: integer
      result_count:
        example: 150
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/model.JobStatus'
        example: running
    type: object
  model.LinkData:
    properties:
      target:
        type: string
      text:
        type: string
      title:
        type: string
      url:
        type: string
    type: object
  model.ListData:
    properties:
      items:
        items:
          type: string
        type: array
      type:
        description: '"ordered" or "unordered"'
        type: string
    type: object
  model.ScrapingConfig:
    properties:
      delay_ms:
        example: 1000
        type: integer
      headers:
        additionalProperties:
          type: string
        type: object
      javascript_enabled:
        example: false
        type: boolean
      max_pages:
        example: 10
        type: integer
      selectors:
        additionalProperties:
          type: string
        example:
          '"price"': '".price"}'
          '{"title"': '"h1"'
        type: object
      timeout:
        example: 30
        type: integer
      url:
        example: https://example.com
        type: string
      user_agent:
        example: Mozilla/5.0...
        type: string
    required:
    - url
    type: object
  model.ScrapingContent:
    properties:
      all_text:
        type: string
      forms:
        items:
          $ref: '#/definitions/model.FormData'
        type: array
      headings:
        items:
          $ref: '#/definitions/model.HeadingData'
        type: array
      images:
        items:
          $ref: '#/definitions/model.ImageData'
        type: array
      links:
        items:
          $ref: '#/definitions/model.LinkData'
        type: array
      lists:
        items:
          $ref: '#/definitions/model.ListData'
        type: array
      main_content:
        type: string
      paragraphs:
        items:
          type: string
        type: array
      structured_data: {}
      tables:
        items:
          $ref: '#/definitions/model.TableData'
        type: array
    type: object
  model.ScrapingMetadata:
    properties:
      charset:
        type: string
      content_length:
        type: integer
      content_type:
        type: string
      headers:
        additionalProperties:
          type: string
        type: object
      javascript_used:
        type: boolean
      language:
        type: string
      last_modified:
        type: string
      response_time_ms:
        type: integer
      user_agent:
        type: string
    type: object
  model.ScrapingResult:
    properties:
      content:
        allOf:
        - $ref: '#/definitions/model.ScrapingContent'
        description: Comprehensive content extraction
      description:
        type: string
      error_msg:
        type: string
      id:
        type: string
      job_id:
        type: string
      metadata:
        $ref: '#/definitions/model.ScrapingMetadata'
      scraped_at:
        type: string
      status_code:
        type: integer
      success:
        type: boolean
      title:
        type: string
      url:
        type: string
      uuid:
        type: string
    type: object
  model.TableData:
    properties:
      headers:
        items:
          type: string
        type: array
      rows:
        items:
          items:
            type: string
          type: array
        type: array
    type: object
  model.UpdateJobRequest:
    properties:
      config:
        $ref: '#/definitions/model.ScrapingConfig'
      description:
        example: Updated description
        type: string
      name:
        example: Updated Product Scraper
        type: string
      priority:
        allOf:
        - $ref: '#/definitions/model.JobPriority'
        example: 3
    type: object
  model.User:
    properties:
      api_key:
        type: string
      created_at:
        type: string
      email:
        type: string
      email_verified:
        type: boolean
      first_name:
        type: string
      id:
        type: string
      is_active:
        type: boolean
      last_login:
        type: string
      last_name:
        type: string
      role:
        $ref: '#/definitions/model.UserRole'
      updated_at:
        type: string
      uuid:
        type: string
    required:
    - email
    type: object
  model.UserRole:
    enum:
    - user
    - admin
    type: string
    x-enum-varnames:
    - UserRoleUser
    - UserRoleAdmin
  monitoring.CheckResult:
    properties:
      latency:
        example: 5ms
        type: string
      message:
        example: Database connection successful
        type: string
      status:
        example: healthy
        type: string
    type: object
  monitoring.HealthStatus:
    properties:
      checks:
        additionalProperties:
          $ref: '#/definitions/monitoring.CheckResult'
        type: object
      metrics:
        $ref: '#/definitions/monitoring.SystemMetrics'
      status:
        example: healthy
        type: string
      timestamp:
        example: "2024-07-29T15:44:02Z"
        type: string
      uptime:
        example: 2h30m45s
        type: string
      version:
        example: 1.0.0
        type: string
    type: object
  monitoring.SystemMetrics:
    properties:
      cpu_usage:
        type: number
      database_connections:
        type: integer
      gc_pause_total_ns:
        type: integer
      goroutine_count:
        type: integer
      heap_size_bytes:
        type: integer
      heap_size_mb:
        type: number
      memory_usage_bytes:
        type: integer
      memory_usage_mb:
        type: number
      timestamp:
        type: string
    type: object
  router.HealthResponse:
    properties:
      status:
        example: ok
        type: string
    type: object
host: localhost:9000
info:
  contact:
    email: <EMAIL>
    name: TrueDax Support Team
    url: https://truedax.com/support
  description: |-
    A comprehensive, production-ready REST API for web scraping operations with advanced features including async job scheduling, content extraction, user management, and audit logging.

    ## Features
    - **Async Job Scheduling**: Priority-based job queue with concurrent processing
    - **Comprehensive Web Scraping**: HTTP and headless Chrome (chromedp) support
    - **Content Extraction**: Headings, paragraphs, links, images, tables, forms, and structured data
    - **Authentication & Authorization**: JWT-based stateless auth with role-based access (user/admin)
    - **Audit Logging**: Comprehensive activity tracking and monitoring
    - **Health Monitoring**: System metrics and health checks

    ## Authentication
    This API uses JWT (JSON Web Token) authentication. To access protected endpoints:
    1. Register a new account using `/auth/register`
    2. Login using `/auth/login` to get access and refresh tokens
    3. Include the access token in the Authorization header: `Bearer <token>`
    4. Use `/auth/refresh` to get new tokens when the access token expires

    ## Rate Limiting
    API endpoints are rate-limited to ensure fair usage and system stability.

    ## Error Handling
    The API returns standard HTTP status codes and JSON error responses with descriptive messages.
  license:
    name: MIT License
    url: https://opensource.org/licenses/MIT
  termsOfService: https://truedax.com/terms
  title: TrueDax Web Scraper API
  version: 1.0.0
paths:
  /admin/audit-logs:
    get:
      consumes:
      - application/json
      description: Retrieve a paginated list of audit logs
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by user ID
        in: query
        name: user_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.AuditLogListResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get audit logs (Admin only)
      tags:
      - admin
  /admin/system-logs:
    get:
      consumes:
      - application/json
      description: Retrieve a paginated list of system logs
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by log level
        in: query
        name: level
        type: string
      - description: Filter by component
        in: query
        name: component
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.SystemLogListResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get system logs (Admin only)
      tags:
      - admin
  /admin/users:
    get:
      consumes:
      - application/json
      description: |-
        Retrieve a comprehensive, paginated list of all users in the system. This endpoint is restricted to administrators only.

        **User Information Included:**
        - Basic profile (email, name, role)
        - Account status (active, locked, disabled)
        - Security metrics (failed login attempts, lock status)
        - Activity timestamps (created, last login)

        **Admin Features:**
        - Filter by role, status, or activity
        - Search by email or name
        - Sort by various criteria
        - Export user data for compliance

        **Security Note:** Sensitive information like passwords and tokens are never included in responses.
      parameters:
      - default: 1
        description: Page number (1-based)
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - description: Filter by user role
        enum:
        - user
        - admin
        in: query
        name: role
        type: string
      - description: Filter by account status
        enum:
        - active
        - inactive
        - locked
        in: query
        name: status
        type: string
      - description: Search by email or name
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved users list with pagination
          schema:
            $ref: '#/definitions/handler.UserListResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: User not authenticated
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "403":
          description: Access denied - admin role required
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal server error during user retrieval
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get all users (Admin only)
      tags:
      - admin
  /auth/login:
    post:
      consumes:
      - application/json
      description: |-
        Authenticate user with email and password credentials. Returns JWT access and refresh tokens for subsequent API calls.

        **Security Features:**
        - Account lockout after multiple failed attempts
        - Password strength validation
        - Device and IP tracking
        - Audit logging of login attempts

        **Rate Limiting:** This endpoint is rate-limited to prevent brute force attacks.
      parameters:
      - description: User login credentials including email, password, and optional
          device info
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/auth.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Successfully authenticated - returns access and refresh tokens
          schema:
            $ref: '#/definitions/auth.TokenPair'
        "400":
          description: Invalid request format or missing required fields
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Invalid credentials, account locked, or account disabled
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "429":
          description: Too many login attempts - rate limit exceeded
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal server error during authentication
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      summary: User login
      tags:
      - auth
  /auth/logout:
    post:
      consumes:
      - application/json
      description: |-
        Logout the current user and revoke all refresh tokens associated with their account. This invalidates all active sessions.

        **Security Features:**
        - Revokes all refresh tokens for the user
        - Invalidates all active sessions across devices
        - Logs the logout event for audit purposes

        **Note:** After logout, the user must login again to access protected endpoints. The access token becomes invalid immediately.
      produces:
      - application/json
      responses:
        "200":
          description: Successfully logged out - all sessions invalidated
          schema:
            $ref: '#/definitions/handler.MessageResponse'
        "401":
          description: User not authenticated or invalid token
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal server error during logout
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: User logout
      tags:
      - auth
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: |-
        Generate a new access token using a valid refresh token. This endpoint should be called when the access token expires.

        **Token Lifecycle:**
        - Access tokens expire in 15 minutes (configurable)
        - Refresh tokens expire in 7 days (configurable)
        - Each refresh generates a new access token
        - Refresh tokens are single-use and automatically rotated

        **Security Features:**
        - Refresh tokens are hashed and stored securely
        - Device and IP tracking for security monitoring
        - Automatic token rotation prevents replay attacks
      parameters:
      - description: Refresh token obtained from login or previous refresh
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/auth.RefreshRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Successfully refreshed - returns new access and refresh tokens
          schema:
            $ref: '#/definitions/auth.TokenPair'
        "400":
          description: Invalid request format or missing refresh token
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Invalid, expired, or revoked refresh token
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal server error during token refresh
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      summary: Refresh access token
      tags:
      - auth
  /auth/register:
    post:
      consumes:
      - application/json
      description: |-
        Create a new user account with email verification and secure password hashing.

        **Password Requirements:**
        - Minimum 8 characters
        - Must contain uppercase, lowercase, number, and special character

        **Account Features:**
        - Email-based authentication
        - Role-based access control (default: user)
        - Audit logging of account creation

        **Note:** All new accounts are created with 'user' role by default. Admin privileges must be granted separately.
      parameters:
      - description: User registration details including email, password, first name,
          and last name
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/auth.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Successfully created user account - returns user details (password
            excluded)
          schema:
            $ref: '#/definitions/model.User'
        "400":
          description: Invalid request format, missing fields, or password doesn't
            meet requirements
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "409":
          description: User with this email already exists
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal server error during registration
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      summary: User registration
      tags:
      - auth
  /health:
    get:
      consumes:
      - application/json
      description: Check if the API is running
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/router.HealthResponse'
      summary: Health check
      tags:
      - health
  /jobs:
    get:
      consumes:
      - application/json
      description: |-
        Retrieve a paginated list of the current user's scraping jobs with filtering and sorting options.

        **Response includes:**
        - Job summary information (ID, name, status, progress)
        - Creation and completion timestamps
        - Result counts and success rates
        - Pagination metadata

        **Job Statuses:**
        - `pending`: Job is queued and waiting to start
        - `running`: Job is currently being processed
        - `completed`: Job finished successfully
        - `failed`: Job encountered an error
        - `cancelled`: Job was cancelled by user or admin
      parameters:
      - default: 1
        description: Page number (1-based)
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        description: Number of items per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - description: Filter by job status
        enum:
        - pending
        - running
        - completed
        - failed
        - cancelled
        in: query
        name: status
        type: string
      - default: -created_at
        description: Sort order
        enum:
        - created_at
        - -created_at
        - name
        - -name
        - status
        - -status
        in: query
        name: sort
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved jobs list with pagination
          schema:
            $ref: '#/definitions/handler.JobListResponse'
        "400":
          description: Invalid query parameters
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: User not authenticated or invalid token
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal server error during job retrieval
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user's scraping jobs
      tags:
      - jobs
    post:
      consumes:
      - application/json
      description: |-
        Create a new asynchronous web scraping job with comprehensive configuration options. Jobs are queued and processed based on priority.

        **Scraping Capabilities:**
        - HTTP client for static content (fast)
        - Headless Chrome for JavaScript-heavy sites
        - Custom selectors for targeted data extraction
        - Configurable delays and timeouts
        - User-agent and header customization

        **Content Extraction:**
        - Headings (H1-H6), paragraphs, links, images
        - Tables, forms, lists, structured data
        - Custom CSS selector-based extraction

        **Job Management:**
        - Priority-based queue processing
        - Progress tracking and status updates
        - Comprehensive result storage in database
      parameters:
      - description: Job configuration including URL, selectors, and scraping options
        in: body
        name: job
        required: true
        schema:
          $ref: '#/definitions/model.CreateJobRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Successfully created job - returns job details with unique
            ID
          schema:
            $ref: '#/definitions/model.Job'
        "400":
          description: Invalid request format, missing required fields, or invalid
            configuration
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: User not authenticated or invalid token
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal server error during job creation
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create a new web scraping job
      tags:
      - jobs
  /jobs/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a completed, failed, or cancelled scraping job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.MessageResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete a job
      tags:
      - jobs
    get:
      consumes:
      - application/json
      description: Retrieve detailed information about a specific scraping job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Job'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get a specific job
      tags:
      - jobs
    put:
      consumes:
      - application/json
      description: Update configuration of a pending scraping job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      - description: Updated job configuration
        in: body
        name: job
        required: true
        schema:
          $ref: '#/definitions/model.UpdateJobRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Job'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update a job
      tags:
      - jobs
  /jobs/{id}/cancel:
    post:
      consumes:
      - application/json
      description: Cancel a pending, queued, or running scraping job
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.MessageResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Cancel a job
      tags:
      - jobs
  /jobs/{id}/queue-position:
    get:
      consumes:
      - application/json
      description: Get the current position of a job in the execution queue
      parameters:
      - description: Job ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.QueuePositionResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get job queue position
      tags:
      - jobs
  /jobs/{id}/results:
    get:
      consumes:
      - application/json
      description: |-
        Retrieve comprehensive results from a completed scraping job with pagination support for large datasets.

        **Result Structure:**
        - **Headings**: H1-H6 tags with text and hierarchy
        - **Paragraphs**: All paragraph text content
        - **Links**: URLs, anchor text, titles, and targets
        - **Images**: URLs, alt text, dimensions
        - **Tables**: Headers and row data
        - **Forms**: Field types, names, and validation rules
        - **Lists**: Ordered and unordered list items
        - **Structured Data**: JSON-LD, microdata, RDFa
        - **Custom Selectors**: User-defined CSS selector results

        **Metadata Included:**
        - Page load times and performance metrics
        - JavaScript execution status
        - HTTP response headers and status codes
        - Error messages and retry information
      parameters:
      - description: Job ID (unique identifier)
        in: path
        name: id
        required: true
        type: string
      - default: 1
        description: Page number for paginated results
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 50
        description: Number of results per page
        in: query
        maximum: 1000
        minimum: 1
        name: limit
        type: integer
      - default: json
        description: Response format
        enum:
        - json
        - csv
        - xlsx
        in: query
        name: format
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved job results with extracted content
          schema:
            $ref: '#/definitions/model.ScrapingResult'
        "400":
          description: Invalid job ID or query parameters
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: User not authenticated or no access to this job
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "404":
          description: Job not found or no results available
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal server error during result retrieval
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get job scraping results
      tags:
      - jobs
  /jobs/stats:
    get:
      consumes:
      - application/json
      description: Retrieve statistics about user's scraping jobs
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.JobStats'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get job statistics
      tags:
      - jobs
  /jobs/test-scrape:
    post:
      consumes:
      - application/json
      description: Test scraping functionality on a single URL without creating a
        job
      parameters:
      - description: URL and selectors to test
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handler.TestScrapeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Scraping test results
          schema:
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handler.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Test scrape a single URL
      tags:
      - jobs
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
tags:
- description: Authentication and authorization endpoints
  name: auth
- description: Job management and web scraping operations
  name: jobs
- description: Administrative endpoints (admin role required)
  name: admin
- description: Health check and system monitoring endpoints
  name: health
