package scraper

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"github.com/PuerkitoBio/goquery"
	"github.com/chromedp/chromedp"
	"go-rest-api/internal/model"
)

// WebScraper implements the Scraper interface for web scraping
type WebScraper struct {
	client   *http.Client
	options  ScraperOptions
	robotsCache map[string]bool
	robotsMutex sync.RWMutex
}

// NewWebScraper creates a new web scraper instance
func NewWebScraper(options ScraperOptions) *WebScraper {
	return &WebScraper{
		client: &http.Client{
			Timeout: options.RequestTimeout,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				if len(via) >= 10 {
					return fmt.Errorf("too many redirects")
				}
				return nil
			},
		},
		options:     options,
		robotsCache: make(map[string]bool),
	}
}

// Scrape performs the web scraping operation
func (ws *WebScraper) Scrape(ctx context.Context, config model.ScrapingConfig, progressCallback ProgressCallback) ([]*ScraperResult, error) {
	if err := ws.ValidateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	// Check robots.txt if enabled
	if ws.options.RespectRobotsTxt {
		if allowed, err := ws.checkRobotsTxt(config.URL); err != nil {
			return nil, fmt.Errorf("robots.txt check failed: %w", err)
		} else if !allowed {
			return nil, fmt.Errorf("scraping not allowed by robots.txt")
		}
	}

	// Determine scraping method based on JavaScript requirement
	if config.JavaScriptEnabled {
		return ws.scrapeWithJavaScript(ctx, config, progressCallback)
	}
	return ws.scrapeWithHTTP(ctx, config, progressCallback)
}

// scrapeWithHTTP performs scraping using HTTP requests and HTML parsing
func (ws *WebScraper) scrapeWithHTTP(ctx context.Context, config model.ScrapingConfig, progressCallback ProgressCallback) ([]*ScraperResult, error) {
	urls := ws.generateURLs(config)
	results := make([]*ScraperResult, 0, len(urls))
	
	// Create semaphore for concurrency control
	semaphore := make(chan struct{}, ws.options.MaxConcurrentRequests)
	var wg sync.WaitGroup
	var resultsMutex sync.Mutex
	
	for i, targetURL := range urls {
		wg.Add(1)
		go func(index int, url string) {
			defer wg.Done()
			
			// Acquire semaphore
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			
			// Check context cancellation
			select {
			case <-ctx.Done():
				return
			default:
			}
			
			// Add delay between requests
			if config.DelayMs > 0 && index > 0 {
				time.Sleep(time.Duration(config.DelayMs) * time.Millisecond)
			}
			
			result := ws.scrapeURL(ctx, url, config)
			
			resultsMutex.Lock()
			results = append(results, result)
			if progressCallback != nil {
				progressCallback(len(results), len(urls), fmt.Sprintf("Scraped %s", url))
			}
			resultsMutex.Unlock()
		}(i, targetURL)
	}
	
	wg.Wait()
	return results, nil
}

// scrapeWithJavaScript performs scraping using headless Chrome for JavaScript-heavy sites
func (ws *WebScraper) scrapeWithJavaScript(ctx context.Context, config model.ScrapingConfig, progressCallback ProgressCallback) ([]*ScraperResult, error) {
	urls := ws.generateURLs(config)
	results := make([]*ScraperResult, 0, len(urls))
	
	// Create Chrome context
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.UserAgent(config.UserAgent),
	)
	
	allocCtx, cancel := chromedp.NewExecAllocator(ctx, opts...)
	defer cancel()
	
	// Process URLs with controlled concurrency
	semaphore := make(chan struct{}, min(ws.options.MaxConcurrentRequests, 2)) // Limit Chrome instances
	var wg sync.WaitGroup
	var resultsMutex sync.Mutex
	
	for i, targetURL := range urls {
		wg.Add(1)
		go func(index int, url string) {
			defer wg.Done()
			
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			
			select {
			case <-ctx.Done():
				return
			default:
			}
			
			if config.DelayMs > 0 && index > 0 {
				time.Sleep(time.Duration(config.DelayMs) * time.Millisecond)
			}
			
			result := ws.scrapeURLWithChrome(allocCtx, url, config)
			
			resultsMutex.Lock()
			results = append(results, result)
			if progressCallback != nil {
				progressCallback(len(results), len(urls), fmt.Sprintf("Scraped %s", url))
			}
			resultsMutex.Unlock()
		}(i, targetURL)
	}
	
	wg.Wait()
	return results, nil
}

// scrapeURL scrapes a single URL using HTTP client
func (ws *WebScraper) scrapeURL(ctx context.Context, targetURL string, config model.ScrapingConfig) *ScraperResult {
	startTime := time.Now()
	result := &ScraperResult{
		URL:       targetURL,
		Timestamp: startTime,
		Data:      make(map[string]interface{}),
	}
	
	// Create request with timeout
	reqCtx, cancel := context.WithTimeout(ctx, time.Duration(config.Timeout)*time.Second)
	defer cancel()
	
	req, err := http.NewRequestWithContext(reqCtx, "GET", targetURL, nil)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to create request: %v", err)
		result.LoadTime = time.Since(startTime)
		return result
	}
	
	// Set headers
	ws.setRequestHeaders(req, config)
	
	// Perform request with retries
	var resp *http.Response
	for attempt := 0; attempt <= ws.options.RetryAttempts; attempt++ {
		resp, err = ws.client.Do(req)
		if err == nil && resp.StatusCode == http.StatusOK {
			break
		}
		
		if resp != nil {
			resp.Body.Close()
		}
		
		if attempt < ws.options.RetryAttempts {
			time.Sleep(ws.options.RetryDelay * time.Duration(attempt+1))
		}
	}
	
	if err != nil {
		result.Error = fmt.Sprintf("Request failed after %d attempts: %v", ws.options.RetryAttempts+1, err)
		result.LoadTime = time.Since(startTime)
		return result
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		result.Error = fmt.Sprintf("HTTP %d: %s", resp.StatusCode, resp.Status)
		result.LoadTime = time.Since(startTime)
		return result
	}

	// Read response body with proper encoding handling
	bodyBytes, err := ws.readResponseBody(resp)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to read response body: %v", err)
		result.LoadTime = time.Since(startTime)
		return result
	}

	// Create document from the properly decoded content
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(bodyBytes)))
	if err != nil {
		result.Error = fmt.Sprintf("Failed to parse HTML: %v", err)
		result.LoadTime = time.Since(startTime)
		return result
	}
	
	// Extract data using selectors
	ws.extractData(doc, config.Selectors, result.Data)
	
	// Set the URL in metadata
	if metadata, ok := result.Data["_metadata"].(map[string]interface{}); ok {
		metadata["url"] = targetURL
	}
	
	result.Success = true
	result.LoadTime = time.Since(startTime)
	return result
}

// scrapeURLWithChrome scrapes a single URL using headless Chrome
func (ws *WebScraper) scrapeURLWithChrome(allocCtx context.Context, targetURL string, config model.ScrapingConfig) *ScraperResult {
	startTime := time.Now()
	result := &ScraperResult{
		URL:       targetURL,
		Timestamp: startTime,
		Data:      make(map[string]interface{}),
	}
	
	// Create Chrome context with timeout
	ctx, cancel := chromedp.NewContext(allocCtx)
	defer cancel()
	
	ctx, cancel = context.WithTimeout(ctx, time.Duration(config.Timeout)*time.Second)
	defer cancel()
	
	var htmlContent string
	err := chromedp.Run(ctx,
		chromedp.Navigate(targetURL),
		chromedp.WaitVisible("body", chromedp.ByQuery),
		chromedp.Sleep(2*time.Second), // Allow JS to load
		chromedp.OuterHTML("html", &htmlContent),
	)
	
	if err != nil {
		result.Error = fmt.Sprintf("Chrome scraping failed: %v", err)
		result.LoadTime = time.Since(startTime)
		return result
	}
	
	// Parse the HTML content
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		result.Error = fmt.Sprintf("Failed to parse HTML: %v", err)
		result.LoadTime = time.Since(startTime)
		return result
	}
	
	// Extract data using selectors
	ws.extractData(doc, config.Selectors, result.Data)
	
	// Set the URL in metadata
	if metadata, ok := result.Data["_metadata"].(map[string]interface{}); ok {
		metadata["url"] = targetURL
	}
	
	result.Success = true
	result.LoadTime = time.Since(startTime)
	return result
}

// extractData extracts data from HTML document using CSS selectors
func (ws *WebScraper) extractData(doc *goquery.Document, selectors map[string]string, data map[string]interface{}) {
	// If no selectors provided, extract main content
	if len(selectors) == 0 {
		ws.extractMainContent(doc, data)
		return
	}

	for field, selector := range selectors {
		var values []string

		// Clean the selector string (remove any JSON escape characters)
		cleanSelector := strings.ReplaceAll(selector, "\\", "")
		cleanSelector = strings.ReplaceAll(cleanSelector, "\"", "")
		cleanSelector = strings.TrimSpace(cleanSelector)

		if cleanSelector == "" {
			data[field] = nil
			continue
		}

		doc.Find(cleanSelector).Each(func(i int, s *goquery.Selection) {
			// Get text content
			text := strings.TrimSpace(s.Text())
			if text != "" {
				values = append(values, text)
				return
			}

			// Try common attributes if no text
			for _, attr := range []string{"href", "src", "alt", "title", "data-value", "data-price"} {
				if value, exists := s.Attr(attr); exists && strings.TrimSpace(value) != "" {
					values = append(values, strings.TrimSpace(value))
					return
				}
			}
		})

		// Store the extracted values
		if len(values) == 0 {
			data[field] = nil
		} else if len(values) == 1 {
			data[field] = values[0]
		} else {
			data[field] = values
		}
	}

	// Add basic metadata
	data["_metadata"] = map[string]interface{}{
		"title":      strings.TrimSpace(doc.Find("title").Text()),
		"url":        "", // Will be set by caller
		"scraped_at": time.Now().Format(time.RFC3339),
	}
}

// extractMainContent extracts ALL meaningful content from a webpage automatically
func (ws *WebScraper) extractMainContent(doc *goquery.Document, data map[string]interface{}) {
	// Extract basic page information
	title := strings.TrimSpace(doc.Find("title").Text())
	description := ""
	if meta := doc.Find("meta[name='description']"); meta.Length() > 0 {
		description, _ = meta.Attr("content")
	}

	// Remove unwanted elements before processing
	doc.Find("script, style, nav, header, footer, aside, .advertisement, .ads, .social-share").Remove()

	// Extract all text content by semantic importance
	content := make(map[string]interface{})

	// 1. Extract headings with hierarchy
	headings := ws.extractHeadings(doc)
	content["headings"] = headings

	// 2. Extract all paragraphs and text blocks
	paragraphs := ws.extractParagraphs(doc)
	content["paragraphs"] = paragraphs

	// 3. Extract all links with context
	links := ws.extractLinks(doc)
	content["links"] = links

	// 4. Extract all images with metadata
	images := ws.extractImages(doc)
	content["images"] = images

	// 5. Extract lists (ordered and unordered)
	lists := ws.extractLists(doc)
	content["lists"] = lists

	// 6. Extract tables if present
	tables := ws.extractTables(doc)
	content["tables"] = tables

	// 7. Extract forms and input elements
	forms := ws.extractForms(doc)
	content["forms"] = forms

	// 8. Extract structured data (JSON-LD, microdata)
	structuredData := ws.extractStructuredData(doc)
	content["structured_data"] = structuredData

	// 9. Extract main content using smart content detection
	mainContent := ws.extractSmartMainContent(doc)
	content["main_content"] = mainContent

	// 10. Extract all visible text as fallback
	allText := ws.extractAllVisibleText(doc)
	content["all_text"] = allText

	// Store all extracted content
	data["title"] = title
	data["description"] = description
	data["content"] = content

	// Calculate statistics
	allTextCombined := strings.Join(paragraphs, " ") + " " + strings.Join(mainContent, " ")
	wordCount := len(strings.Fields(allTextCombined))

	// Add comprehensive metadata
	data["_metadata"] = map[string]interface{}{
		"title":              title,
		"description":        description,
		"url":               "", // Will be set by caller
		"scraped_at":        time.Now().Format(time.RFC3339),
		"word_count":        wordCount,
		"paragraph_count":   len(paragraphs),
		"heading_count":     len(headings),
		"link_count":        len(links),
		"image_count":       len(images),
		"list_count":        len(lists),
		"table_count":       len(tables),
		"form_count":        len(forms),
		"has_structured_data": len(structuredData) > 0,
		"content_length":    len(allTextCombined),
		"extraction_method": "comprehensive_auto",
	}
}

// generateURLs generates the list of URLs to scrape based on configuration
func (ws *WebScraper) generateURLs(config model.ScrapingConfig) []string {
	baseURL := config.URL
	maxPages := config.MaxPages

	if maxPages <= 1 {
		return []string{baseURL}
	}

	urls := make([]string, 0, maxPages)

	// Try to detect pagination patterns
	if ws.containsPaginationPattern(baseURL) {
		urls = ws.generatePaginatedURLs(baseURL, maxPages)
	} else {
		// If no pagination pattern detected, just scrape the base URL
		urls = append(urls, baseURL)
	}

	return urls
}

// containsPaginationPattern checks if URL contains common pagination patterns
func (ws *WebScraper) containsPaginationPattern(url string) bool {
	patterns := []string{
		`page=\d+`,
		`p=\d+`,
		`offset=\d+`,
		`start=\d+`,
		`/page/\d+`,
		`/p\d+`,
	}

	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, url); matched {
			return true
		}
	}
	return false
}

// generatePaginatedURLs generates URLs for pagination
func (ws *WebScraper) generatePaginatedURLs(baseURL string, maxPages int) []string {
	urls := make([]string, 0, maxPages)

	// Common pagination patterns
	patterns := map[string]string{
		`page=\d+`:     "page=",
		`p=\d+`:        "p=",
		`offset=\d+`:   "offset=",
		`start=\d+`:    "start=",
		`/page/\d+`:    "/page/",
		`/p\d+`:        "/p",
	}

	for pattern, replacement := range patterns {
		if matched, _ := regexp.MatchString(pattern, baseURL); matched {
			for i := 1; i <= maxPages; i++ {
				var newURL string
				if strings.Contains(replacement, "=") {
					// Query parameter pattern
					re := regexp.MustCompile(pattern)
					newURL = re.ReplaceAllString(baseURL, replacement+fmt.Sprintf("%d", i))
				} else {
					// Path parameter pattern
					re := regexp.MustCompile(pattern)
					newURL = re.ReplaceAllString(baseURL, replacement+fmt.Sprintf("%d", i))
				}
				urls = append(urls, newURL)
			}
			return urls
		}
	}

	// If no pattern matched, try adding page parameter
	parsedURL, err := url.Parse(baseURL)
	if err == nil {
		for i := 1; i <= maxPages; i++ {
			query := parsedURL.Query()
			query.Set("page", fmt.Sprintf("%d", i))
			parsedURL.RawQuery = query.Encode()
			urls = append(urls, parsedURL.String())
		}
	}

	return urls
}

// setRequestHeaders sets appropriate headers for HTTP requests
func (ws *WebScraper) setRequestHeaders(req *http.Request, config model.ScrapingConfig) {
	// Set User-Agent
	if config.UserAgent != "" {
		req.Header.Set("User-Agent", config.UserAgent)
	} else {
		req.Header.Set("User-Agent", "TrueDax-WebScraper/1.0")
	}

	// Set custom headers
	for key, value := range config.Headers {
		req.Header.Set(key, value)
	}

	// Set common headers
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	// Let Go's HTTP client handle compression automatically
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
}

// readResponseBody reads and properly decodes the HTTP response body
func (ws *WebScraper) readResponseBody(resp *http.Response) ([]byte, error) {
	// Go's HTTP client automatically handles gzip/deflate decompression
	// but we need to ensure proper charset handling

	// Read the body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// Detect and handle charset encoding
	contentType := resp.Header.Get("Content-Type")

	// If content type indicates a specific charset, handle it
	if strings.Contains(strings.ToLower(contentType), "charset=") {
		// Extract charset
		parts := strings.Split(strings.ToLower(contentType), "charset=")
		if len(parts) > 1 {
			charset := strings.TrimSpace(strings.Split(parts[1], ";")[0])

			// Handle common non-UTF-8 encodings
			switch charset {
			case "iso-8859-1", "latin-1":
				// Convert Latin-1 to UTF-8
				return ws.convertLatin1ToUTF8(bodyBytes), nil
			case "windows-1252":
				// Convert Windows-1252 to UTF-8
				return ws.convertWindows1252ToUTF8(bodyBytes), nil
			}
		}
	}

	// Try to detect if content is valid UTF-8
	if !utf8.Valid(bodyBytes) {
		// If not valid UTF-8, try to convert from common encodings
		if converted := ws.tryConvertToUTF8(bodyBytes); converted != nil {
			return converted, nil
		}
	}

	return bodyBytes, nil
}

// convertLatin1ToUTF8 converts Latin-1 (ISO-8859-1) encoded bytes to UTF-8
func (ws *WebScraper) convertLatin1ToUTF8(input []byte) []byte {
	var result []byte
	for _, b := range input {
		if b < 128 {
			result = append(result, b)
		} else {
			// Convert Latin-1 to UTF-8
			result = append(result, 0xC0|(b>>6), 0x80|(b&0x3F))
		}
	}
	return result
}

// convertWindows1252ToUTF8 converts Windows-1252 encoded bytes to UTF-8
func (ws *WebScraper) convertWindows1252ToUTF8(input []byte) []byte {
	// Simplified conversion - for full implementation, use golang.org/x/text
	return ws.convertLatin1ToUTF8(input) // Fallback to Latin-1 conversion
}

// tryConvertToUTF8 attempts to convert bytes to UTF-8 from common encodings
func (ws *WebScraper) tryConvertToUTF8(input []byte) []byte {
	// Try Latin-1 conversion first
	converted := ws.convertLatin1ToUTF8(input)
	if utf8.Valid(converted) {
		return converted
	}

	// If still not valid, return original
	return nil
}

// checkRobotsTxt checks if scraping is allowed by robots.txt
func (ws *WebScraper) checkRobotsTxt(targetURL string) (bool, error) {
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return false, err
	}

	robotsURL := fmt.Sprintf("%s://%s/robots.txt", parsedURL.Scheme, parsedURL.Host)

	// Check cache first
	ws.robotsMutex.RLock()
	if allowed, exists := ws.robotsCache[robotsURL]; exists {
		ws.robotsMutex.RUnlock()
		return allowed, nil
	}
	ws.robotsMutex.RUnlock()

	// Fetch robots.txt
	resp, err := ws.client.Get(robotsURL)
	if err != nil {
		// If robots.txt is not accessible, assume allowed
		return true, nil
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		// No robots.txt file, scraping is allowed
		ws.robotsMutex.Lock()
		ws.robotsCache[robotsURL] = true
		ws.robotsMutex.Unlock()
		return true, nil
	}

	// For simplicity, we'll assume scraping is allowed
	// In a production environment, you'd want to properly parse robots.txt
	ws.robotsMutex.Lock()
	ws.robotsCache[robotsURL] = true
	ws.robotsMutex.Unlock()

	return true, nil
}

// ValidateConfig validates the scraping configuration
func (ws *WebScraper) ValidateConfig(config model.ScrapingConfig) error {
	if config.URL == "" {
		return fmt.Errorf("URL is required")
	}

	if _, err := url.Parse(config.URL); err != nil {
		return fmt.Errorf("invalid URL: %w", err)
	}

	if config.MaxPages < 1 {
		return fmt.Errorf("max pages must be at least 1")
	}

	if config.MaxPages > 1000 {
		return fmt.Errorf("max pages cannot exceed 1000")
	}

	if config.DelayMs < 0 {
		return fmt.Errorf("delay cannot be negative")
	}

	if config.Timeout < 1 {
		return fmt.Errorf("timeout must be at least 1 second")
	}

	if config.Timeout > 300 {
		return fmt.Errorf("timeout cannot exceed 300 seconds")
	}

	// Note: Selectors are optional - if empty, comprehensive auto-extraction will be used

	return nil
}

// EstimatePages estimates the number of pages that will be scraped
func (ws *WebScraper) EstimatePages(config model.ScrapingConfig) (int, error) {
	if config.MaxPages <= 1 {
		return 1, nil
	}

	if ws.containsPaginationPattern(config.URL) {
		return config.MaxPages, nil
	}

	return 1, nil
}

// Helper function for min
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// extractHeadings extracts all headings with their hierarchy level
func (ws *WebScraper) extractHeadings(doc *goquery.Document) []map[string]interface{} {
	var headings []map[string]interface{}

	doc.Find("h1, h2, h3, h4, h5, h6").Each(func(i int, s *goquery.Selection) {
		text := strings.TrimSpace(s.Text())
		if text != "" {
			level := 1
			tagName := goquery.NodeName(s)
			if len(tagName) == 2 && tagName[0] == 'h' {
				if l := int(tagName[1] - '0'); l >= 1 && l <= 6 {
					level = l
				}
			}

			headings = append(headings, map[string]interface{}{
				"text":  text,
				"level": level,
				"tag":   tagName,
			})
		}
	})

	return headings
}

// extractParagraphs extracts all paragraph content
func (ws *WebScraper) extractParagraphs(doc *goquery.Document) []string {
	var paragraphs []string

	doc.Find("p").Each(func(i int, s *goquery.Selection) {
		text := strings.TrimSpace(s.Text())
		if len(text) > 10 { // Filter out very short paragraphs
			paragraphs = append(paragraphs, text)
		}
	})

	return paragraphs
}

// extractLinks extracts all links with their context
func (ws *WebScraper) extractLinks(doc *goquery.Document) []map[string]interface{} {
	var links []map[string]interface{}

	doc.Find("a[href]").Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		if exists && href != "" {
			linkText := strings.TrimSpace(s.Text())
			title, _ := s.Attr("title")

			link := map[string]interface{}{
				"url":  href,
				"text": linkText,
			}

			if title != "" {
				link["title"] = title
			}

			// Determine link type
			if strings.HasPrefix(href, "mailto:") {
				link["type"] = "email"
			} else if strings.HasPrefix(href, "tel:") {
				link["type"] = "phone"
			} else if strings.HasPrefix(href, "http") {
				link["type"] = "external"
			} else {
				link["type"] = "internal"
			}

			links = append(links, link)
		}
	})

	return links
}

// extractImages extracts all images with metadata
func (ws *WebScraper) extractImages(doc *goquery.Document) []map[string]interface{} {
	var images []map[string]interface{}

	doc.Find("img").Each(func(i int, s *goquery.Selection) {
		src, exists := s.Attr("src")
		if exists && src != "" {
			image := map[string]interface{}{
				"src": src,
			}

			if alt, exists := s.Attr("alt"); exists {
				image["alt"] = alt
			}

			if title, exists := s.Attr("title"); exists {
				image["title"] = title
			}

			if width, exists := s.Attr("width"); exists {
				image["width"] = width
			}

			if height, exists := s.Attr("height"); exists {
				image["height"] = height
			}

			images = append(images, image)
		}
	})

	return images
}

// extractLists extracts all lists (ordered and unordered)
func (ws *WebScraper) extractLists(doc *goquery.Document) []map[string]interface{} {
	var lists []map[string]interface{}

	doc.Find("ul, ol").Each(func(i int, s *goquery.Selection) {
		var items []string
		s.Find("li").Each(func(j int, li *goquery.Selection) {
			text := strings.TrimSpace(li.Text())
			if text != "" {
				items = append(items, text)
			}
		})

		if len(items) > 0 {
			listType := "unordered"
			if goquery.NodeName(s) == "ol" {
				listType = "ordered"
			}

			lists = append(lists, map[string]interface{}{
				"type":  listType,
				"items": items,
			})
		}
	})

	return lists
}

// extractTables extracts all tables with their data
func (ws *WebScraper) extractTables(doc *goquery.Document) []map[string]interface{} {
	var tables []map[string]interface{}

	doc.Find("table").Each(func(i int, table *goquery.Selection) {
		var headers []string
		var rows [][]string

		// Extract headers
		table.Find("thead tr th, tr:first-child th").Each(func(j int, th *goquery.Selection) {
			header := strings.TrimSpace(th.Text())
			headers = append(headers, header)
		})

		// Extract rows
		table.Find("tbody tr, tr").Each(func(j int, tr *goquery.Selection) {
			var row []string
			tr.Find("td, th").Each(func(k int, td *goquery.Selection) {
				cell := strings.TrimSpace(td.Text())
				row = append(row, cell)
			})
			if len(row) > 0 {
				rows = append(rows, row)
			}
		})

		if len(rows) > 0 {
			tableData := map[string]interface{}{
				"rows": rows,
			}
			if len(headers) > 0 {
				tableData["headers"] = headers
			}
			tables = append(tables, tableData)
		}
	})

	return tables
}

// extractForms extracts all forms and their input elements
func (ws *WebScraper) extractForms(doc *goquery.Document) []map[string]interface{} {
	var forms []map[string]interface{}

	doc.Find("form").Each(func(i int, form *goquery.Selection) {
		action, _ := form.Attr("action")
		method, _ := form.Attr("method")

		var inputs []map[string]interface{}
		form.Find("input, textarea, select").Each(func(j int, input *goquery.Selection) {
			inputType, _ := input.Attr("type")
			name, _ := input.Attr("name")
			placeholder, _ := input.Attr("placeholder")

			inputData := map[string]interface{}{
				"tag":  goquery.NodeName(input),
				"type": inputType,
				"name": name,
			}

			if placeholder != "" {
				inputData["placeholder"] = placeholder
			}

			inputs = append(inputs, inputData)
		})

		formData := map[string]interface{}{
			"action": action,
			"method": method,
			"inputs": inputs,
		}

		forms = append(forms, formData)
	})

	return forms
}

// extractStructuredData extracts JSON-LD and microdata
func (ws *WebScraper) extractStructuredData(doc *goquery.Document) []map[string]interface{} {
	var structuredData []map[string]interface{}

	// Extract JSON-LD
	doc.Find("script[type='application/ld+json']").Each(func(i int, s *goquery.Selection) {
		jsonText := strings.TrimSpace(s.Text())
		if jsonText != "" {
			structuredData = append(structuredData, map[string]interface{}{
				"type":    "json-ld",
				"content": jsonText,
			})
		}
	})

	// Extract microdata (basic extraction)
	doc.Find("[itemscope]").Each(func(i int, s *goquery.Selection) {
		itemType, _ := s.Attr("itemtype")
		if itemType != "" {
			structuredData = append(structuredData, map[string]interface{}{
				"type":      "microdata",
				"itemtype":  itemType,
				"content":   strings.TrimSpace(s.Text()),
			})
		}
	})

	return structuredData
}

// extractSmartMainContent uses intelligent content detection
func (ws *WebScraper) extractSmartMainContent(doc *goquery.Document) []string {
	var mainContent []string

	// Try common content containers with scoring
	contentSelectors := []string{
		"main",
		"article",
		"[role='main']",
		"#content",
		".content",
		"#main",
		".main",
		".post-content",
		".entry-content",
		".article-content",
		".page-content",
		".story-body",
		".article-body",
		"section",
	}

	bestContent := ""
	bestScore := 0

	for _, selector := range contentSelectors {
		doc.Find(selector).Each(func(i int, s *goquery.Selection) {
			text := strings.TrimSpace(s.Text())
			score := len(strings.Fields(text)) // Word count as simple scoring

			if score > bestScore && score > 50 { // Minimum word threshold
				bestContent = text
				bestScore = score
			}
		})
	}

	if bestContent != "" {
		mainContent = append(mainContent, bestContent)
	} else {
		// Fallback: collect all meaningful paragraphs
		doc.Find("p").Each(func(i int, s *goquery.Selection) {
			text := strings.TrimSpace(s.Text())
			if len(strings.Fields(text)) > 10 { // At least 10 words
				mainContent = append(mainContent, text)
			}
		})
	}

	return mainContent
}

// extractAllVisibleText extracts all visible text as a fallback
func (ws *WebScraper) extractAllVisibleText(doc *goquery.Document) string {
	// Remove non-content elements
	doc.Find("script, style, nav, header, footer, aside, .advertisement, .ads").Remove()

	bodyText := strings.TrimSpace(doc.Find("body").Text())

	// Clean up excessive whitespace
	lines := strings.Split(bodyText, "\n")
	var cleanLines []string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 5 { // Filter out very short lines
			cleanLines = append(cleanLines, line)
		}
	}

	return strings.Join(cleanLines, "\n")
}
