package service

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"go-rest-api/internal/model"
	"go-rest-api/internal/scraper"
	"github.com/google/uuid"
)

// JobService handles business logic for job management
type JobService struct {
	jobs           map[string]*model.Job
	queue          []*model.Job
	mutex          sync.RWMutex
	worker         *JobWorker
	scraperManager *scraper.ScraperManager
}

// JobWorker manages the execution of jobs
type JobWorker struct {
	maxConcurrentJobs int
	runningJobs       map[string]bool
	mutex             sync.RWMutex
	jobService        *JobService
}

// NewJobService creates a new job service instance
func NewJobService() *JobService {
	// Initialize scraper manager with results directory
	scraperOptions := scraper.DefaultScraperOptions()
	scraperManager := scraper.NewScraperManager("./results", scraperOptions)
	
	js := &JobService{
		jobs:           make(map[string]*model.Job),
		queue:          make([]*model.Job, 0),
		scraperManager: scraperManager,
	}
	js.worker = &JobWorker{
		maxConcurrentJobs: 5, // Configurable based on cloud resources
		runningJobs:       make(map[string]bool),
		jobService:        js,
	}
	
	// Start the job processor
	go js.worker.processJobs()
	
	// Start cleanup routine for old results (runs every 24 hours)
	go js.startCleanupRoutine()
	
	return js
}

// CreateJob creates a new scraping job
func (js *JobService) CreateJob(userEmail string, req model.CreateJobRequest) (*model.Job, error) {
	js.mutex.Lock()
	defer js.mutex.Unlock()

	// Validate the request
	if err := js.validateJobRequest(req); err != nil {
		return nil, err
	}

	job := &model.Job{
		ID:          generateJobID(),
		UserEmail:   userEmail,
		Name:        req.Name,
		Description: req.Description,
		Config:      req.Config,
		Status:      model.JobStatusPending,
		Priority:    req.Priority,
		Progress:    0,
		CreatedAt:   time.Now().UTC(),
		ResultCount: 0,
	}

	js.jobs[job.ID] = job
	js.addToQueue(job)

	return job, nil
}

// GetJob retrieves a job by ID
func (js *JobService) GetJob(jobID, userEmail string) (*model.Job, error) {
	js.mutex.RLock()
	defer js.mutex.RUnlock()

	job, exists := js.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job not found")
	}

	if job.UserEmail != userEmail {
		return nil, fmt.Errorf("unauthorized access to job")
	}

	return job, nil
}

// GetUserJobs retrieves all jobs for a user with pagination
func (js *JobService) GetUserJobs(userEmail string, offset, limit int) ([]*model.JobSummary, int, error) {
	js.mutex.RLock()
	defer js.mutex.RUnlock()

	var userJobs []*model.Job
	for _, job := range js.jobs {
		if job.UserEmail == userEmail {
			userJobs = append(userJobs, job)
		}
	}

	// Sort by creation time (newest first)
	sort.Slice(userJobs, func(i, j int) bool {
		return userJobs[i].CreatedAt.After(userJobs[j].CreatedAt)
	})

	total := len(userJobs)
	
	// Apply pagination
	start := offset
	if start > total {
		start = total
	}
	
	end := start + limit
	if end > total {
		end = total
	}

	paginatedJobs := userJobs[start:end]
	summaries := make([]*model.JobSummary, len(paginatedJobs))
	
	for i, job := range paginatedJobs {
		summaries[i] = &model.JobSummary{
			ID:          job.ID,
			Name:        job.Name,
			Status:      job.Status,
			Priority:    job.Priority,
			Progress:    job.Progress,
			CreatedAt:   job.CreatedAt,
			ResultCount: job.ResultCount,
		}
	}

	return summaries, total, nil
}

// UpdateJob updates an existing job
func (js *JobService) UpdateJob(jobID, userEmail string, req model.UpdateJobRequest) (*model.Job, error) {
	js.mutex.Lock()
	defer js.mutex.Unlock()

	job, exists := js.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job not found")
	}

	if job.UserEmail != userEmail {
		return nil, fmt.Errorf("unauthorized access to job")
	}

	// Can only update pending jobs
	if job.Status != model.JobStatusPending {
		return nil, fmt.Errorf("can only update pending jobs")
	}

	// Update fields if provided
	if req.Name != nil {
		job.Name = *req.Name
	}
	if req.Description != nil {
		job.Description = *req.Description
	}
	if req.Config != nil {
		job.Config = *req.Config
	}
	if req.Priority != nil {
		job.Priority = *req.Priority
		js.reorderQueue() // Reorder queue based on new priority
	}

	return job, nil
}

// CancelJob cancels a job
func (js *JobService) CancelJob(jobID, userEmail string) error {
	js.mutex.Lock()
	defer js.mutex.Unlock()

	job, exists := js.jobs[jobID]
	if !exists {
		return fmt.Errorf("job not found")
	}

	if job.UserEmail != userEmail {
		return fmt.Errorf("unauthorized access to job")
	}

	// Can only cancel pending, queued, or running jobs
	if job.Status == model.JobStatusCompleted || job.Status == model.JobStatusFailed || job.Status == model.JobStatusCancelled {
		return fmt.Errorf("cannot cancel job in status: %s", job.Status)
	}

	job.Status = model.JobStatusCancelled
	js.removeFromQueue(jobID)

	return nil
}

// DeleteJob deletes a job
func (js *JobService) DeleteJob(jobID, userEmail string) error {
	js.mutex.Lock()
	defer js.mutex.Unlock()

	job, exists := js.jobs[jobID]
	if !exists {
		return fmt.Errorf("job not found")
	}

	if job.UserEmail != userEmail {
		return fmt.Errorf("unauthorized access to job")
	}

	// Can only delete completed, failed, or cancelled jobs
	if job.Status == model.JobStatusPending || job.Status == model.JobStatusQueued || job.Status == model.JobStatusRunning {
		return fmt.Errorf("cannot delete active job")
	}

	delete(js.jobs, jobID)
	return nil
}

// GetJobStats returns statistics about jobs for a user
func (js *JobService) GetJobStats(userEmail string) (*model.JobStats, error) {
	js.mutex.RLock()
	defer js.mutex.RUnlock()

	stats := &model.JobStats{}

	for _, job := range js.jobs {
		if job.UserEmail != userEmail {
			continue
		}

		stats.TotalJobs++
		switch job.Status {
		case model.JobStatusPending:
			stats.PendingJobs++
		case model.JobStatusQueued:
			stats.PendingJobs++
		case model.JobStatusRunning:
			stats.RunningJobs++
		case model.JobStatusCompleted:
			stats.CompletedJobs++
		case model.JobStatusFailed:
			stats.FailedJobs++
		}
	}

	return stats, nil
}

// GetQueuePosition returns the position of a job in the queue
func (js *JobService) GetQueuePosition(jobID string) (int, error) {
	js.mutex.RLock()
	defer js.mutex.RUnlock()

	for i, job := range js.queue {
		if job.ID == jobID {
			return i + 1, nil
		}
	}

	return 0, fmt.Errorf("job not found in queue")
}

// Helper methods

func (js *JobService) validateJobRequest(req model.CreateJobRequest) error {
	if req.Name == "" {
		return fmt.Errorf("job name is required")
	}
	if req.Config.URL == "" {
		return fmt.Errorf("URL is required")
	}
	if req.Priority < model.PriorityLow || req.Priority > model.PriorityCritical {
		return fmt.Errorf("invalid priority level")
	}
	return nil
}

func (js *JobService) addToQueue(job *model.Job) {
	job.Status = model.JobStatusQueued
	js.queue = append(js.queue, job)
	js.reorderQueue()
}

func (js *JobService) removeFromQueue(jobID string) {
	for i, job := range js.queue {
		if job.ID == jobID {
			js.queue = append(js.queue[:i], js.queue[i+1:]...)
			break
		}
	}
}

func (js *JobService) reorderQueue() {
	// Sort by priority (higher first), then by creation time (older first)
	sort.Slice(js.queue, func(i, j int) bool {
		if js.queue[i].Priority != js.queue[j].Priority {
			return js.queue[i].Priority > js.queue[j].Priority
		}
		return js.queue[i].CreatedAt.Before(js.queue[j].CreatedAt)
	})
}

// GetJobResults retrieves the results for a completed job
func (js *JobService) GetJobResults(jobID, userEmail string) (*scraper.ScrapingResults, error) {
	js.mutex.RLock()
	job, exists := js.jobs[jobID]
	js.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("job not found")
	}

	if job.UserEmail != userEmail {
		return nil, fmt.Errorf("unauthorized access to job")
	}

	if job.Status != model.JobStatusCompleted {
		return nil, fmt.Errorf("job is not completed yet")
	}

	return js.scraperManager.GetResults(jobID)
}

// TestScrapeURL tests scraping a single URL without creating a job
func (js *JobService) TestScrapeURL(config model.ScrapingConfig, userEmail string) (map[string]interface{}, error) {
	// Create a test scraper
	scraperOptions := scraper.GetOptimalScraperOptions(config)
	testScraper := scraper.NewWebScraper(scraperOptions)
	
	// Validate the configuration
	if err := testScraper.ValidateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}
	
	// Perform the scraping
	ctx := context.Background()
	results, err := testScraper.Scrape(ctx, config, nil)
	if err != nil {
		return nil, fmt.Errorf("scraping failed: %w", err)
	}
	
	if len(results) == 0 {
		return nil, fmt.Errorf("no results returned")
	}

	// Print Results for debugging
	for _, result := range results {
		fmt.Printf("Scraped URL: %s, Success: %t, Data: %v, Load Time: %v, Timestamp: %s\n",
			result.URL, result.Success, result.Data, result.LoadTime, result.Timestamp)
	}
	

	// Return the first result
	result := results[0]
	response := map[string]interface{}{
		"url":       result.URL,
		"success":   result.Success,
		"data":      result.Data,
		"load_time": result.LoadTime.String(),
		"timestamp": result.Timestamp,
	}
	
	if result.Error != "" {
		response["error"] = result.Error
	}
	
	return response, nil
}

// startCleanupRoutine starts a background routine to clean up old result files
func (js *JobService) startCleanupRoutine() {
	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		// Clean up results older than 30 days
		if err := js.scraperManager.CleanupOldResults(30 * 24 * time.Hour); err != nil {
			fmt.Printf("Error cleaning up old results: %v\n", err)
		}
	}
}

func generateJobID() string {
	return "job_" + uuid.New().String()[:8]
}

// Job Worker methods

func (jw *JobWorker) processJobs() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		jw.checkAndStartJobs()
	}
}

func (jw *JobWorker) checkAndStartJobs() {
	jw.mutex.Lock()
	runningCount := len(jw.runningJobs)
	jw.mutex.Unlock()

	if runningCount >= jw.maxConcurrentJobs {
		return
	}

	jw.jobService.mutex.Lock()
	defer jw.jobService.mutex.Unlock()

	for i, job := range jw.jobService.queue {
		if job.Status == model.JobStatusQueued {
			jw.mutex.Lock()
			currentRunning := len(jw.runningJobs)
			jw.mutex.Unlock()

			if currentRunning >= jw.maxConcurrentJobs {
				break
			}

			// Start the job
			go jw.executeJob(job)
			
			// Remove from queue
			jw.jobService.queue = append(jw.jobService.queue[:i], jw.jobService.queue[i+1:]...)
			break
		}
	}
}

func (jw *JobWorker) executeJob(job *model.Job) {
	jw.mutex.Lock()
	jw.runningJobs[job.ID] = true
	jw.mutex.Unlock()

	defer func() {
		jw.mutex.Lock()
		delete(jw.runningJobs, job.ID)
		jw.mutex.Unlock()
	}()

	// Update job status
	jw.jobService.mutex.Lock()
	job.Status = model.JobStatusRunning
	startTime := time.Now().UTC()
	job.StartedAt = &startTime
	jw.jobService.mutex.Unlock()

	// Create context for the job execution
	ctx := context.Background()
	
	// Create progress callback to update job progress
	progressCallback := func(current, total int, message string) {
		jw.jobService.mutex.Lock()
		if total > 0 {
			job.Progress = int(float64(current) / float64(total) * 100)
		}
		jw.jobService.mutex.Unlock()
	}

	// Execute the actual scraping job
	err := jw.jobService.scraperManager.ExecuteJob(ctx, job, progressCallback)

	jw.jobService.mutex.Lock()
	completedTime := time.Now().UTC()
	job.CompletedAt = &completedTime
	job.Progress = 100

	if err != nil {
		job.Status = model.JobStatusFailed
		job.ErrorMsg = err.Error()
	} else {
		job.Status = model.JobStatusCompleted
		// ResultURL and ResultCount are already set by the scraper manager
	}
	jw.jobService.mutex.Unlock()
}


