package middleware

import (
	"bytes"
	"io"
	"strings"

	"go-rest-api/internal/services/audit"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuditMiddleware provides audit logging middleware
type AuditMiddleware struct {
	auditService *audit.AuditService
}

// NewAuditMiddleware creates a new AuditMiddleware
func NewAuditMiddleware(auditService *audit.AuditService) *AuditMiddleware {
	return &AuditMiddleware{
		auditService: auditService,
	}
}

// AuditLogger middleware that logs certain actions automatically
func (m *AuditMiddleware) AuditLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip audit logging for certain endpoints
		if shouldSkipAudit(c.Request.URL.Path, c.Request.Method) {
			c.Next()
			return
		}

		// Get user ID from context
		userIDInterface, exists := c.Get("user_id")
		if !exists {
			c.Next()
			return
		}

		userID := userIDInterface.(uuid.UUID)
		path := c.Request.URL.Path
		method := c.Request.Method

		// Read request body for POST/PUT requests
		var requestBody []byte
		if method == "POST" || method == "PUT" {
			if c.Request.Body != nil {
				requestBody, _ = io.ReadAll(c.Request.Body)
				c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
			}
		}

		// Process the request
		c.Next()

		// Log the action based on the endpoint and method
		go func() {
			action, resource, resourceID := determineActionAndResource(path, method)
			if action != "" && resource != "" {
				var details *string
				if len(requestBody) > 0 && len(requestBody) < 1000 { // Limit body size for logging
					bodyStr := string(requestBody)
					details = &bodyStr
				}

				m.auditService.LogAction(userID, action, resource, resourceID, details, c)
			}
		}()
	}
}

// shouldSkipAudit determines if audit logging should be skipped for certain endpoints
func shouldSkipAudit(path, method string) bool {
	// Skip health checks, swagger docs, and other non-sensitive endpoints
	skipPaths := []string{
		"/health",
		"/swagger",
		"/auth/refresh", // Too frequent
	}

	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}

	// Skip GET requests for most endpoints (except admin endpoints)
	if method == "GET" && !strings.HasPrefix(path, "/admin") {
		return true
	}

	return false
}

// determineActionAndResource determines the action and resource based on the path and method
func determineActionAndResource(path, method string) (action, resource string, resourceID *string) {
	// Parse job-related endpoints
	if strings.HasPrefix(path, "/jobs") {
		resource = "job"
		
		// Extract job ID if present
		parts := strings.Split(path, "/")
		if len(parts) >= 3 && parts[2] != "" {
			jobID := parts[2]
			resourceID = &jobID
		}

		switch method {
		case "POST":
			if strings.Contains(path, "/cancel") {
				action = "cancel"
			} else {
				action = "create"
			}
		case "PUT":
			action = "update"
		case "DELETE":
			action = "delete"
		}
	}

	// Parse auth-related endpoints
	if strings.HasPrefix(path, "/auth") {
		resource = "auth"
		if strings.Contains(path, "/login") {
			action = "login"
		} else if strings.Contains(path, "/logout") {
			action = "logout"
		} else if strings.Contains(path, "/register") {
			action = "register"
		}
	}

	// Parse admin-related endpoints
	if strings.HasPrefix(path, "/admin") {
		resource = "admin"
		action = "view"
		if strings.Contains(path, "/users") {
			resource = "admin_users"
		} else if strings.Contains(path, "/audit-logs") {
			resource = "admin_audit_logs"
		} else if strings.Contains(path, "/system-logs") {
			resource = "admin_system_logs"
		}
	}

	return action, resource, resourceID
}
