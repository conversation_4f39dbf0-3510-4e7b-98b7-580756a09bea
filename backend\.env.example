# TrueDax Web Scraper Environment Configuration
# Copy this file to .env and customize the values

# Server Configuration
PORT=9000
GIN_MODE=release

# Database Configuration
MONGODB_URI=""
# Alternative local MongoDB settings (if using local Docker)
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=truedax_scraper
MONGODB_USERNAME=truedax_user
MONGODB_PASSWORD=truedax_secure_password_2024
MONGODB_MAX_POOL_SIZE=100
MONGODB_MIN_POOL_SIZE=5
MONGODB_TIMEOUT=10s

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-change-in-production-2024
JWT_ACCESS_TOKEN_DURATION=15m
JWT_REFRESH_TOKEN_DURATION=7d

# Scraper Configuration
MAX_CONCURRENT_JOBS=5
RESULTS_DIRECTORY=/app/results
CLEANUP_INTERVAL=24h
MAX_RESULT_AGE=720h

# Chrome Configuration for JavaScript scraping
CHROME_BIN=/usr/bin/chromium-browser
CHROME_PATH=/usr/bin/chromium-browser
CHROMIUM_FLAGS=--no-sandbox --disable-dev-shm-usage --disable-gpu --headless

# Rate Limiting
REQUESTS_PER_MINUTE=60
BURST_SIZE=10

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout

# Security Configuration
BCRYPT_COST=12
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION=30m

# Redis (for job queue optimization - optional)
# REDIS_URL=redis://localhost:6379/0
