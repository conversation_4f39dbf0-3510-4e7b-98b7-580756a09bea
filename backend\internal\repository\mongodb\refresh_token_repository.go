package mongodb

import (
	"context"
	"fmt"
	"time"

	"go-rest-api/config"
	"go-rest-api/internal/model"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type RefreshTokenRepository struct {
	collection *mongo.Collection
}

func NewRefreshTokenRepository() *RefreshTokenRepository {
	return &RefreshTokenRepository{
		collection: config.GetCollection("refresh_tokens"),
	}
}

// Create creates a new refresh token
func (r *RefreshTokenRepository) Create(ctx context.Context, token *model.RefreshToken) error {
	token.BeforeInsert()
	
	result, err := r.collection.InsertOne(ctx, token)
	if err != nil {
		return fmt.Errorf("failed to create refresh token: %w", err)
	}
	
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		token.ID = oid
	}
	
	return nil
}

// GetByTokenHash retrieves a refresh token by token hash
func (r *RefreshTokenRepository) GetByTokenHash(ctx context.Context, tokenHash string) (*model.RefreshToken, error) {
	var token model.RefreshToken
	filter := bson.M{
		"token_hash": tokenHash,
		"revoked_at": nil,
		"expires_at": bson.M{"$gt": time.Now().UTC()},
	}
	
	err := r.collection.FindOne(ctx, filter).Decode(&token)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("refresh token not found")
		}
		return nil, fmt.Errorf("failed to get refresh token: %w", err)
	}
	
	return &token, nil
}

// GetByUserID retrieves all active refresh tokens for a user
func (r *RefreshTokenRepository) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*model.RefreshToken, error) {
	filter := bson.M{
		"user_id":    userID,
		"revoked_at": nil,
		"expires_at": bson.M{"$gt": time.Now().UTC()},
	}
	
	opts := options.Find().SetSort(bson.D{{Key: "created_at", Value: -1}})
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get refresh tokens by user ID: %w", err)
	}
	defer cursor.Close(ctx)
	
	var tokens []*model.RefreshToken
	for cursor.Next(ctx) {
		var token model.RefreshToken
		if err := cursor.Decode(&token); err != nil {
			return nil, fmt.Errorf("failed to decode refresh token: %w", err)
		}
		tokens = append(tokens, &token)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return tokens, nil
}

// RevokeToken revokes a refresh token
func (r *RefreshTokenRepository) RevokeToken(ctx context.Context, tokenHash string) error {
	filter := bson.M{"token_hash": tokenHash}
	update := bson.M{
		"$set": bson.M{
			"revoked_at": time.Now().UTC(),
		},
	}
	
	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to revoke refresh token: %w", err)
	}
	
	if result.MatchedCount == 0 {
		return fmt.Errorf("refresh token not found")
	}
	
	return nil
}

// RevokeAllUserTokens revokes all refresh tokens for a user
func (r *RefreshTokenRepository) RevokeAllUserTokens(ctx context.Context, userID uuid.UUID) error {
	filter := bson.M{
		"user_id":    userID,
		"revoked_at": nil,
	}
	update := bson.M{
		"$set": bson.M{
			"revoked_at": time.Now().UTC(),
		},
	}
	
	_, err := r.collection.UpdateMany(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to revoke all user tokens: %w", err)
	}
	
	return nil
}

// DeleteExpiredTokens deletes all expired refresh tokens
func (r *RefreshTokenRepository) DeleteExpiredTokens(ctx context.Context) error {
	filter := bson.M{
		"expires_at": bson.M{"$lt": time.Now().UTC()},
	}
	
	_, err := r.collection.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete expired tokens: %w", err)
	}
	
	return nil
}

// CleanupRevokedTokens deletes revoked tokens older than the specified duration
func (r *RefreshTokenRepository) CleanupRevokedTokens(ctx context.Context, olderThan time.Duration) error {
	cutoff := time.Now().UTC().Add(-olderThan)
	filter := bson.M{
		"revoked_at": bson.M{
			"$ne":  nil,
			"$lt": cutoff,
		},
	}
	
	_, err := r.collection.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to cleanup revoked tokens: %w", err)
	}
	
	return nil
}

// Count returns the total number of active refresh tokens
func (r *RefreshTokenRepository) Count(ctx context.Context) (int64, error) {
	filter := bson.M{
		"revoked_at": nil,
		"expires_at": bson.M{"$gt": time.Now().UTC()},
	}
	
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count refresh tokens: %w", err)
	}
	
	return count, nil
}

// CountByUserID returns the number of active refresh tokens for a user
func (r *RefreshTokenRepository) CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error) {
	filter := bson.M{
		"user_id":    userID,
		"revoked_at": nil,
		"expires_at": bson.M{"$gt": time.Now().UTC()},
	}
	
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count user refresh tokens: %w", err)
	}
	
	return count, nil
}
