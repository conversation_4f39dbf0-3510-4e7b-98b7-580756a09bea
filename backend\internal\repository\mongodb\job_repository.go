package mongodb

import (
	"context"
	"fmt"
	"time"

	"go-rest-api/config"
	"go-rest-api/internal/model"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type JobRepository struct {
	collection *mongo.Collection
}

func NewJobRepository() *JobRepository {
	return &JobRepository{
		collection: config.GetCollection("jobs"),
	}
}

// Create creates a new job
func (r *JobRepository) Create(ctx context.Context, job *model.Job) error {
	if job.CreatedAt.IsZero() {
		job.CreatedAt = time.Now().UTC()
	}
	if job.UpdatedAt.IsZero() {
		job.UpdatedAt = time.Now().UTC()
	}
	if job.Status == "" {
		job.Status = model.JobStatusPending
	}
	if job.Priority == 0 {
		job.Priority = model.PriorityNormal
	}
	
	result, err := r.collection.InsertOne(ctx, job)
	if err != nil {
		return fmt.Errorf("failed to create job: %w", err)
	}
	
	if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
		job.ID = oid
	}
	
	return nil
}

// GetByID retrieves a job by ID
func (r *JobRepository) GetByID(ctx context.Context, jobID string) (*model.Job, error) {
	var job model.Job
	filter := bson.M{"job_id": jobID}
	
	err := r.collection.FindOne(ctx, filter).Decode(&job)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("job not found")
		}
		return nil, fmt.Errorf("failed to get job by ID: %w", err)
	}
	
	return &job, nil
}

// GetByUserID retrieves jobs for a specific user with pagination
func (r *JobRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*model.Job, error) {
	filter := bson.M{"user_id": userID}
	
	opts := options.Find()
	opts.SetLimit(int64(limit))
	opts.SetSkip(int64(offset))
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}})
	
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs by user ID: %w", err)
	}
	defer cursor.Close(ctx)
	
	var jobs []*model.Job
	for cursor.Next(ctx) {
		var job model.Job
		if err := cursor.Decode(&job); err != nil {
			return nil, fmt.Errorf("failed to decode job: %w", err)
		}
		jobs = append(jobs, &job)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return jobs, nil
}

// Update updates a job
func (r *JobRepository) Update(ctx context.Context, job *model.Job) error {
	job.UpdatedAt = time.Now().UTC()
	
	filter := bson.M{"job_id": job.JobID}
	update := bson.M{"$set": job}
	
	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update job: %w", err)
	}
	
	if result.MatchedCount == 0 {
		return fmt.Errorf("job not found")
	}
	
	return nil
}

// UpdateStatus updates the status of a job
func (r *JobRepository) UpdateStatus(ctx context.Context, jobID string, status model.JobStatus) error {
	filter := bson.M{"job_id": jobID}
	update := bson.M{
		"$set": bson.M{
			"status":     status,
			"updated_at": time.Now().UTC(),
		},
	}
	
	// Set started_at when status changes to running
	if status == model.JobStatusRunning {
		update["$set"].(bson.M)["started_at"] = time.Now().UTC()
	}
	
	// Set completed_at when status changes to completed or failed
	if status == model.JobStatusCompleted || status == model.JobStatusFailed {
		update["$set"].(bson.M)["completed_at"] = time.Now().UTC()
	}
	
	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update job status: %w", err)
	}
	
	if result.MatchedCount == 0 {
		return fmt.Errorf("job not found")
	}
	
	return nil
}

// UpdateProgress updates the progress of a job
func (r *JobRepository) UpdateProgress(ctx context.Context, jobID string, progress int) error {
	filter := bson.M{"job_id": jobID}
	update := bson.M{
		"$set": bson.M{
			"progress":   progress,
			"updated_at": time.Now().UTC(),
		},
	}
	
	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update job progress: %w", err)
	}
	
	if result.MatchedCount == 0 {
		return fmt.Errorf("job not found")
	}
	
	return nil
}

// Delete deletes a job
func (r *JobRepository) Delete(ctx context.Context, jobID string) error {
	filter := bson.M{"job_id": jobID}
	
	result, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete job: %w", err)
	}
	
	if result.DeletedCount == 0 {
		return fmt.Errorf("job not found")
	}
	
	return nil
}

// List retrieves jobs with pagination and optional filtering
func (r *JobRepository) List(ctx context.Context, limit, offset int, status *model.JobStatus) ([]*model.Job, error) {
	filter := bson.M{}
	if status != nil {
		filter["status"] = *status
	}
	
	opts := options.Find()
	opts.SetLimit(int64(limit))
	opts.SetSkip(int64(offset))
	opts.SetSort(bson.D{{Key: "created_at", Value: -1}})
	
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to list jobs: %w", err)
	}
	defer cursor.Close(ctx)
	
	var jobs []*model.Job
	for cursor.Next(ctx) {
		var job model.Job
		if err := cursor.Decode(&job); err != nil {
			return nil, fmt.Errorf("failed to decode job: %w", err)
		}
		jobs = append(jobs, &job)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return jobs, nil
}

// Count returns the total number of jobs
func (r *JobRepository) Count(ctx context.Context) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, fmt.Errorf("failed to count jobs: %w", err)
	}
	
	return count, nil
}

// CountByUserID returns the number of jobs for a specific user
func (r *JobRepository) CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error) {
	filter := bson.M{"user_id": userID}
	
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count user jobs: %w", err)
	}
	
	return count, nil
}

// CountByStatus returns the number of jobs with a specific status
func (r *JobRepository) CountByStatus(ctx context.Context, status model.JobStatus) (int64, error) {
	filter := bson.M{"status": status}
	
	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count jobs by status: %w", err)
	}
	
	return count, nil
}

// GetPendingJobs retrieves jobs that are pending execution
func (r *JobRepository) GetPendingJobs(ctx context.Context, limit int) ([]*model.Job, error) {
	filter := bson.M{"status": model.JobStatusPending}
	
	opts := options.Find()
	opts.SetLimit(int64(limit))
	opts.SetSort(bson.D{{Key: "priority", Value: -1}, {Key: "created_at", Value: 1}})
	
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending jobs: %w", err)
	}
	defer cursor.Close(ctx)
	
	var jobs []*model.Job
	for cursor.Next(ctx) {
		var job model.Job
		if err := cursor.Decode(&job); err != nil {
			return nil, fmt.Errorf("failed to decode job: %w", err)
		}
		jobs = append(jobs, &job)
	}
	
	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}
	
	return jobs, nil
}
