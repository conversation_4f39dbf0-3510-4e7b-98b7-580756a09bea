package audit

import (
	"context"
	"time"

	"go-rest-api/internal/model"
	"go-rest-api/internal/repository"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuditService handles audit logging operations
type AuditService struct {
	repos *repository.Repositories
}

// NewAuditService creates a new AuditService
func NewAuditService(repos *repository.Repositories) *AuditService {
	return &AuditService{
		repos: repos,
	}
}

// LogAction logs a user action for audit purposes
func (s *AuditService) LogAction(userID uuid.UUID, action, resource string, resourceID *string, details *string, c *gin.Context) error {
	var resourceIDStr string
	if resourceID != nil {
		resourceIDStr = *resourceID
	}

	auditLog := &model.AuditLog{
		UserID:       &userID,
		Action:       action,
		ResourceType: resource,
		ResourceID:   resourceIDStr,
		IPAddress:    c.ClientIP(),
		UserAgent:    c<PERSON>("User-Agent"),
		Details:      details,
		CreatedAt:    time.Now(),
	}

	return s.repos.Audit.CreateAuditLog(context.Background(), auditLog)
}

// LogLogin logs a successful login
func (s *AuditService) LogLogin(userID uuid.UUID, c *gin.Context) error {
	return s.LogAction(userID, "login", "auth", nil, nil, c)
}

// LogLogout logs a logout
func (s *AuditService) LogLogout(userID uuid.UUID, c *gin.Context) error {
	return s.LogAction(userID, "logout", "auth", nil, nil, c)
}

// LogJobCreation logs job creation
func (s *AuditService) LogJobCreation(userID uuid.UUID, jobID string, c *gin.Context) error {
	return s.LogAction(userID, "create", "job", &jobID, nil, c)
}

// LogJobCancellation logs job cancellation
func (s *AuditService) LogJobCancellation(userID uuid.UUID, jobID string, c *gin.Context) error {
	return s.LogAction(userID, "cancel", "job", &jobID, nil, c)
}

// LogJobDeletion logs job deletion
func (s *AuditService) LogJobDeletion(userID uuid.UUID, jobID string, c *gin.Context) error {
	return s.LogAction(userID, "delete", "job", &jobID, nil, c)
}

// LogPasswordChange logs password change
func (s *AuditService) LogPasswordChange(userID uuid.UUID, c *gin.Context) error {
	return s.LogAction(userID, "password_change", "user", nil, nil, c)
}

// LogFailedLogin logs a failed login attempt
func (s *AuditService) LogFailedLogin(email string, c *gin.Context) error {
	details := "Failed login attempt for email: " + email
	return s.LogAction(uuid.Nil, "failed_login", "auth", nil, &details, c)
}

// GetUserAuditLogs retrieves audit logs for a specific user
func (s *AuditService) GetUserAuditLogs(userEmail string, limit, offset int) ([]*model.AuditLog, error) {
	return s.repos.Audit.GetUserAuditLogs(context.Background(), userEmail, limit, offset)
}

// GetAuditLogs retrieves audit logs with pagination
func (s *AuditService) GetAuditLogs(limit, offset int) ([]*model.AuditLog, error) {
	return s.repos.Audit.GetAuditLogs(context.Background(), limit, offset)
}
