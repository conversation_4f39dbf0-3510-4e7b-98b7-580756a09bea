package mongodb

import "go-rest-api/internal/repository"

// NewRepositories creates and returns a new Repositories instance with MongoDB implementations
func NewRepositories() *repository.Repositories {
	return &repository.Repositories{
		User:         NewUserRepository(),
		RefreshToken: NewRefreshTokenRepository(),
		Job:          NewJobRepository(),
		Scraper:      NewScraperRepository(),
		Audit:        NewAuditRepository(),
	}
}
