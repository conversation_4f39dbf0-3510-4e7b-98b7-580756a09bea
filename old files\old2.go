package scraper

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"go-rest-api/internal/model"
)

// ScraperManager manages different types of scrapers and handles result storage
type ScraperManager struct {
	scrapers     map[string]Scraper
	resultsDir   string
	options      ScraperOptions
}

// NewScraperManager creates a new scraper manager
func NewScraperManager(resultsDir string, options ScraperOptions) *ScraperManager {
	// Ensure results directory exists
	os.MkdirAll(resultsDir, 0755)
	
	manager := &ScraperManager{
		scrapers:   make(map[string]Scraper),
		resultsDir: resultsDir,
		options:    options,
	}
	
	// Register default scrapers
	manager.RegisterScraper("web", NewWebScraper(options))
	
	return manager
}

// RegisterScraper registers a new scraper type
func (sm *ScraperManager) RegisterScraper(name string, scraper Scraper) {
	sm.scrapers[name] = scraper
}

// GetScraper returns a scraper by name
func (sm *ScraperManager) GetScraper(name string) (Scraper, error) {
	scraper, exists := sm.scrapers[name]
	if !exists {
		return nil, fmt.Errorf("scraper '%s' not found", name)
	}
	return scraper, nil
}

// ExecuteJob executes a scraping job and stores the results
func (sm *ScraperManager) ExecuteJob(ctx context.Context, job *model.Job, progressCallback ProgressCallback) error {
	// Get the appropriate scraper (for now, we only have web scraper)
	scraper, err := sm.GetScraper("web")
	if err != nil {
		return fmt.Errorf("failed to get scraper: %w", err)
	}
	
	// Validate configuration
	if err := scraper.ValidateConfig(job.Config); err != nil {
		return fmt.Errorf("invalid scraping configuration: %w", err)
	}
	
	// Start scraping
	startTime := time.Now()
	results, err := scraper.Scrape(ctx, job.Config, progressCallback)
	if err != nil {
		return fmt.Errorf("scraping failed: %w", err)
	}
	
	// Store results to file and get the file path
	resultFilePath, err := sm.storeResults(job.ID, results)
	if err != nil {
		return fmt.Errorf("failed to store results: %w", err)
	}
	
	// Update job with results
	job.ResultCount = len(results)
	job.ResultURL = resultFilePath // This will be the local file path
	
	// Calculate success rate
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}
	
	if len(results) > 0 {
		successRate := float64(successCount) / float64(len(results)) * 100
		if successRate < 50 {
			return fmt.Errorf("scraping success rate too low: %.1f%%", successRate)
		}
	}
	
	duration := time.Since(startTime)
	fmt.Printf("Scraping completed in %v. Success rate: %.1f%% (%d/%d)\n", 
		duration, float64(successCount)/float64(len(results))*100, successCount, len(results))
	fmt.Printf("Results saved to: %s\n", resultFilePath)
	
	return nil
}

// storeResults stores scraping results to a file
func (sm *ScraperManager) storeResults(jobID string, results []*ScraperResult) (string, error) {
	// Create filename with timestamp
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("%s_%s.json", jobID, timestamp)
	filePath := filepath.Join(sm.resultsDir, filename)
	
	// Get absolute path for the result
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to get absolute path: %w", err)
	}
	
	// Create results summary with detailed information
	summary := ScrapingResults{
		JobID:       jobID,
		Timestamp:   time.Now(),
		TotalPages:  len(results),
		SuccessRate: sm.calculateSuccessRate(results),
		Results:     results,
		FilePath:    absPath, // Add file path to the results
	}
	
	// Write to file with pretty formatting
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to create results file: %w", err)
	}
	defer file.Close()
	
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(summary); err != nil {
		return "", fmt.Errorf("failed to encode results: %w", err)
	}
	
	fmt.Printf("Results saved to: %s\n", absPath)
	fmt.Printf("Total results: %d, Success rate: %.1f%%\n", len(results), summary.SuccessRate)
	
	// Return the absolute file path
	return absPath, nil
}

// calculateSuccessRate calculates the success rate of scraping results
func (sm *ScraperManager) calculateSuccessRate(results []*ScraperResult) float64 {
	if len(results) == 0 {
		return 0
	}
	
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}
	
	return float64(successCount) / float64(len(results)) * 100
}

// GetResults retrieves stored results for a job
func (sm *ScraperManager) GetResults(jobID string) (*ScrapingResults, error) {
	// Find the most recent results file for the job
	files, err := filepath.Glob(filepath.Join(sm.resultsDir, jobID+"_*.json"))
	if err != nil {
		return nil, fmt.Errorf("failed to search for results: %w", err)
	}
	
	if len(files) == 0 {
		return nil, fmt.Errorf("no results found for job %s", jobID)
	}
	
	// Get the most recent file (files are sorted alphabetically, which works for our timestamp format)
	latestFile := files[len(files)-1]
	
	// Read and parse the file
	file, err := os.Open(latestFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open results file: %w", err)
	}
	defer file.Close()
	
	var results ScrapingResults
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&results); err != nil {
		return nil, fmt.Errorf("failed to decode results: %w", err)
	}
	
	return &results, nil
}

// CleanupOldResults removes old result files to save space
func (sm *ScraperManager) CleanupOldResults(maxAge time.Duration) error {
	files, err := filepath.Glob(filepath.Join(sm.resultsDir, "*.json"))
	if err != nil {
		return fmt.Errorf("failed to list result files: %w", err)
	}
	
	cutoff := time.Now().Add(-maxAge)
	
	for _, file := range files {
		info, err := os.Stat(file)
		if err != nil {
			continue
		}
		
		if info.ModTime().Before(cutoff) {
			if err := os.Remove(file); err != nil {
				fmt.Printf("Failed to remove old result file %s: %v\n", file, err)
			}
		}
	}
	
	return nil
}

// ScrapingResults represents the complete results of a scraping job
type ScrapingResults struct {
	JobID       string           `json:"job_id"`
	Timestamp   time.Time        `json:"timestamp"`
	TotalPages  int              `json:"total_pages"`
	SuccessRate float64          `json:"success_rate"`
	FilePath    string           `json:"file_path"` // Local file path where results are stored
	Results     []*ScraperResult `json:"results"`
}

// ScraperFactory provides methods to create different types of scrapers
type ScraperFactory struct{}

// CreateWebScraper creates a new web scraper with specified options
func (sf *ScraperFactory) CreateWebScraper(options ScraperOptions) Scraper {
	return NewWebScraper(options)
}

// CreateScraperFromConfig creates a scraper based on configuration
func (sf *ScraperFactory) CreateScraperFromConfig(config model.ScrapingConfig) (Scraper, error) {
	options := DefaultScraperOptions()
	
	// Adjust options based on config
	if config.JavaScriptEnabled {
		options.MaxConcurrentRequests = min(options.MaxConcurrentRequests, 2) // Limit Chrome instances
	}
	
	if config.DelayMs > 0 {
		// Adjust request timeout if delays are long
		if config.DelayMs > 5000 {
			options.RequestTimeout = time.Duration(config.Timeout+10) * time.Second
		}
	}
	
	return NewWebScraper(options), nil
}

// GetOptimalScraperOptions returns optimal scraper options based on job configuration
func GetOptimalScraperOptions(config model.ScrapingConfig) ScraperOptions {
	options := DefaultScraperOptions()
	
	// Adjust based on expected load
	if config.MaxPages > 100 {
		options.MaxConcurrentRequests = 3 // Reduce for large jobs
	} else if config.MaxPages > 10 {
		options.MaxConcurrentRequests = 5
	} else {
		options.MaxConcurrentRequests = 8 // Increase for small jobs
	}
	
	// Adjust timeout based on config
	if config.Timeout > 0 {
		options.RequestTimeout = time.Duration(config.Timeout) * time.Second
	}
	
	// Adjust retry behavior for reliability
	if config.JavaScriptEnabled {
		options.RetryAttempts = 2 // Fewer retries for JS-enabled scraping
		options.RetryDelay = 3 * time.Second
	}
	
	return options
}
